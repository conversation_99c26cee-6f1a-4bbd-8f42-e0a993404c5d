"""
坦克大战游戏主逻辑
"""
import pygame
import random
import sys
from utils import *
from tank import Tank
from bullet import Bullet
from obstacle import ObstacleManager

class Game:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("坦克大战")
        self.clock = pygame.time.Clock()
        
        # 游戏状态
        self.state = GAME_MENU
        self.running = True
        
        # 字体
        self.font_large = pygame.font.Font(None, 48)
        self.font_medium = pygame.font.Font(None, 32)
        self.font_small = pygame.font.Font(None, 24)
        
        # 游戏对象
        self.player_tank = None
        self.enemy_tanks = []
        self.bullets = []
        self.obstacle_manager = ObstacleManager()
        
        # 游戏统计
        self.score = 0
        self.enemies_killed = 0
        self.level = 1
        self.enemies_per_level = 5
        
        self.init_game()
    
    def init_game(self):
        """初始化游戏"""
        # 创建玩家坦克
        self.player_tank = Tank(100, 500, "player")
        
        # 创建敌方坦克
        self.enemy_tanks = []
        self.spawn_enemies()
        
        # 清空子弹
        self.bullets = []
        
        # 重置统计
        self.enemies_killed = 0
    
    def spawn_enemies(self):
        """生成敌方坦克"""
        spawn_positions = [
            (700, 100), (600, 100), (700, 200),
            (100, 100), (200, 100), (300, 100)
        ]
        
        for i in range(min(self.enemies_per_level, len(spawn_positions))):
            pos = spawn_positions[i]
            # 检查生成位置是否与障碍物冲突
            temp_rect = pygame.Rect(pos[0] - TANK_SIZE//2, pos[1] - TANK_SIZE//2, 
                                  TANK_SIZE, TANK_SIZE)
            if not self.obstacle_manager.check_collision(temp_rect):
                enemy = Tank(pos[0], pos[1], "enemy")
                self.enemy_tanks.append(enemy)
    
    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if self.state == GAME_MENU:
                    if event.key == pygame.K_SPACE:
                        self.state = GAME_PLAYING
                        self.init_game()
                
                elif self.state == GAME_PLAYING:
                    if event.key == pygame.K_SPACE:
                        # 玩家射击
                        bullet = self.player_tank.shoot()
                        if bullet:
                            self.bullets.append(bullet)
                    elif event.key == pygame.K_ESCAPE:
                        self.state = GAME_MENU
                
                elif self.state in [GAME_OVER, GAME_WIN]:
                    if event.key == pygame.K_r:
                        self.state = GAME_PLAYING
                        self.score = 0
                        self.level = 1
                        self.init_game()
                    elif event.key == pygame.K_ESCAPE:
                        self.state = GAME_MENU
    
    def update(self):
        """更新游戏状态"""
        if self.state != GAME_PLAYING:
            return
        
        # 获取按键状态
        keys_pressed = pygame.key.get_pressed()
        
        # 更新玩家坦克
        if self.player_tank:
            self.player_tank.update(keys_pressed, self.obstacle_manager)
        
        # 更新敌方坦克
        for enemy in self.enemy_tanks[:]:
            bullet = enemy.update(obstacles=self.obstacle_manager)
            if bullet:
                self.bullets.append(bullet)
        
        # 更新子弹
        for bullet in self.bullets[:]:
            bullet.update()
            
            # 移除非活跃子弹
            if not bullet.active:
                self.bullets.remove(bullet)
                continue
            
            # 检查子弹与障碍物碰撞
            if self.obstacle_manager.handle_bullet_collision(bullet):
                continue
            
            # 检查子弹与坦克碰撞
            self.check_bullet_tank_collision(bullet)
        
        # 检查游戏结束条件
        self.check_game_state()
    
    def check_bullet_tank_collision(self, bullet):
        """检查子弹与坦克的碰撞"""
        # 玩家子弹击中敌方坦克
        if bullet.owner_type == "player":
            for enemy in self.enemy_tanks[:]:
                if bullet.get_rect().colliderect(enemy.get_rect()):
                    bullet.destroy()
                    if enemy.take_damage():
                        self.enemy_tanks.remove(enemy)
                        self.enemies_killed += 1
                        self.score += 100
                    break
        
        # 敌方子弹击中玩家坦克
        elif bullet.owner_type == "enemy" and self.player_tank:
            if bullet.get_rect().colliderect(self.player_tank.get_rect()):
                bullet.destroy()
                if self.player_tank.take_damage():
                    self.state = GAME_OVER
    
    def check_game_state(self):
        """检查游戏状态"""
        # 检查是否消灭所有敌人
        if not self.enemy_tanks:
            if self.level < 3:  # 假设有3关
                self.level += 1
                self.spawn_enemies()
            else:
                self.state = GAME_WIN
    
    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(BLACK)
        
        if self.state == GAME_MENU:
            self.draw_menu()
        elif self.state == GAME_PLAYING:
            self.draw_game()
        elif self.state == GAME_OVER:
            self.draw_game_over()
        elif self.state == GAME_WIN:
            self.draw_game_win()
        
        pygame.display.flip()
    
    def draw_menu(self):
        """绘制菜单"""
        title = self.font_large.render("坦克大战", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        self.screen.blit(title, title_rect)
        
        instruction = self.font_medium.render("按空格键开始游戏", True, WHITE)
        instruction_rect = instruction.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(instruction, instruction_rect)
        
        controls = [
            "游戏控制:",
            "WASD - 移动坦克",
            "空格 - 射击",
            "ESC - 返回菜单"
        ]
        
        for i, text in enumerate(controls):
            color = YELLOW if i == 0 else WHITE
            control_text = self.font_small.render(text, True, color)
            control_rect = control_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 100 + i*30))
            self.screen.blit(control_text, control_rect)
    
    def draw_game(self):
        """绘制游戏画面"""
        # 绘制障碍物
        self.obstacle_manager.draw(self.screen)
        
        # 绘制坦克
        if self.player_tank:
            self.player_tank.draw(self.screen)
        
        for enemy in self.enemy_tanks:
            enemy.draw(self.screen)
        
        # 绘制子弹
        for bullet in self.bullets:
            bullet.draw(self.screen)
        
        # 绘制UI
        self.draw_ui()
    
    def draw_ui(self):
        """绘制用户界面"""
        # 分数
        score_text = self.font_medium.render(f"分数: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 10))
        
        # 关卡
        level_text = self.font_medium.render(f"关卡: {self.level}", True, WHITE)
        self.screen.blit(level_text, (10, 50))
        
        # 敌人数量
        enemies_text = self.font_medium.render(f"敌人: {len(self.enemy_tanks)}", True, WHITE)
        self.screen.blit(enemies_text, (10, 90))
    
    def draw_game_over(self):
        """绘制游戏结束画面"""
        self.draw_game()  # 先绘制游戏画面
        
        # 半透明覆盖层
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        # 游戏结束文本
        game_over_text = self.font_large.render("游戏结束", True, RED)
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        self.screen.blit(game_over_text, game_over_rect)
        
        score_text = self.font_medium.render(f"最终分数: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(score_text, score_rect)
        
        restart_text = self.font_medium.render("按R重新开始，ESC返回菜单", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        self.screen.blit(restart_text, restart_rect)
    
    def draw_game_win(self):
        """绘制胜利画面"""
        self.draw_game()  # 先绘制游戏画面
        
        # 半透明覆盖层
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        # 胜利文本
        win_text = self.font_large.render("恭喜通关!", True, GREEN)
        win_rect = win_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        self.screen.blit(win_text, win_rect)
        
        score_text = self.font_medium.render(f"最终分数: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(score_text, score_rect)
        
        restart_text = self.font_medium.render("按R重新开始，ESC返回菜单", True, WHITE)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        self.screen.blit(restart_text, restart_rect)
    
    def run(self):
        """运行游戏主循环"""
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()
