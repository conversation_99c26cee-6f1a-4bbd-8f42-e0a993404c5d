"""
道具系统
"""
import pygame
import random
import time
from utils import *

class PowerUp:
    def __init__(self, x, y, powerup_type):
        self.x = x
        self.y = y
        self.type = powerup_type  # "wall_breaker" 或 "shield"
        self.size = 25
        self.rect = pygame.Rect(x - self.size//2, y - self.size//2, self.size, self.size)
        self.spawn_time = time.time()
        self.active = True
        
        # 动画效果
        self.animation_timer = 0
        self.pulse_scale = 1.0
        
        # 道具属性
        if powerup_type == "wall_breaker":
            self.color = (255, 165, 0)  # 橙色
            self.name = "Wall Breaker"
            self.description = "Can destroy steel walls"
        elif powerup_type == "shield":
            self.color = (0, 191, 255)  # 深天蓝色
            self.name = "Shield"
            self.description = "Immune to bullets"
    
    def update(self):
        """更新道具状态"""
        if not self.active:
            return
        
        # 检查是否超时（30秒后消失）
        if time.time() - self.spawn_time > 30:
            self.active = False
            return
        
        # 更新动画
        self.animation_timer += 1
        self.pulse_scale = 1.0 + 0.2 * abs(pygame.math.Vector2(1, 0).rotate(self.animation_timer * 5).x)
    
    def draw(self, screen):
        """绘制道具"""
        if not self.active:
            return
        
        # 计算动画大小
        animated_size = int(self.size * self.pulse_scale)
        animated_rect = pygame.Rect(
            self.x - animated_size//2, 
            self.y - animated_size//2, 
            animated_size, 
            animated_size
        )
        
        # 绘制道具主体
        pygame.draw.rect(screen, self.color, animated_rect)
        pygame.draw.rect(screen, WHITE, animated_rect, 2)
        
        # 绘制道具图标
        if self.type == "wall_breaker":
            # 绘制锤子图标
            center_x, center_y = self.x, self.y
            # 锤子柄
            pygame.draw.line(screen, BLACK, 
                           (center_x - 5, center_y + 5), 
                           (center_x + 5, center_y - 5), 3)
            # 锤子头
            pygame.draw.rect(screen, BLACK, 
                           (center_x - 8, center_y - 8, 6, 6))
        
        elif self.type == "shield":
            # 绘制盾牌图标
            center_x, center_y = self.x, self.y
            # 盾牌外形
            points = [
                (center_x, center_y - 8),
                (center_x - 6, center_y - 4),
                (center_x - 6, center_y + 4),
                (center_x, center_y + 8),
                (center_x + 6, center_y + 4),
                (center_x + 6, center_y - 4)
            ]
            pygame.draw.polygon(screen, BLACK, points)
        
        # 绘制闪烁效果
        if int(self.animation_timer / 10) % 2:
            pygame.draw.rect(screen, WHITE, animated_rect, 1)
    
    def get_rect(self):
        """获取碰撞矩形"""
        return self.rect
    
    def collect(self):
        """收集道具"""
        self.active = False

class PowerUpManager:
    def __init__(self):
        self.current_powerup = None
        self.last_spawn_time = 0
        self.spawn_interval = random.randint(15, 25)  # 15-25秒随机生成
        
        # 道具类型
        self.powerup_types = ["wall_breaker", "shield"]
    
    def update(self, obstacles):
        """更新道具管理器"""
        current_time = time.time()
        
        # 更新当前道具
        if self.current_powerup:
            self.current_powerup.update()
            if not self.current_powerup.active:
                self.current_powerup = None
        
        # 生成新道具
        if (self.current_powerup is None and 
            current_time - self.last_spawn_time > self.spawn_interval):
            self.spawn_powerup(obstacles)
            self.last_spawn_time = current_time
            self.spawn_interval = random.randint(15, 25)
    
    def spawn_powerup(self, obstacles):
        """生成道具"""
        # 随机选择道具类型
        powerup_type = random.choice(self.powerup_types)
        
        # 寻找合适的生成位置
        max_attempts = 50
        for _ in range(max_attempts):
            x = random.randint(50, SCREEN_WIDTH - 50)
            y = random.randint(50, SCREEN_HEIGHT - 50)
            
            # 创建临时矩形检查碰撞
            temp_rect = pygame.Rect(x - 15, y - 15, 30, 30)
            
            # 检查是否与障碍物冲突
            if not obstacles.check_collision(temp_rect):
                self.current_powerup = PowerUp(x, y, powerup_type)
                break
    
    def check_collection(self, tank):
        """检查坦克是否收集到道具"""
        if (self.current_powerup and self.current_powerup.active and
            tank.get_rect().colliderect(self.current_powerup.get_rect())):
            
            powerup_type = self.current_powerup.type
            self.current_powerup.collect()
            return powerup_type
        return None
    
    def draw(self, screen):
        """绘制道具"""
        if self.current_powerup:
            self.current_powerup.draw(screen)
    
    def get_current_powerup(self):
        """获取当前道具"""
        return self.current_powerup
