"""
子弹类定义
"""
import pygame
import math
from utils import *

class Bullet:
    def __init__(self, x, y, angle, owner_type="player"):
        self.x = x
        self.y = y
        self.angle = angle
        self.speed = BULLET_SPEED
        self.size = BULLET_SIZE
        self.owner_type = owner_type  # "player" 或 "enemy"
        self.active = True
        
        # 计算移动向量
        rad = math.radians(angle)
        self.dx = math.cos(rad) * self.speed
        self.dy = math.sin(rad) * self.speed
        
        # 创建碰撞矩形
        self.rect = pygame.Rect(x - self.size//2, y - self.size//2, self.size, self.size)
    
    def update(self):
        """更新子弹位置"""
        if not self.active:
            return
            
        # 移动子弹
        self.x += self.dx
        self.y += self.dy
        
        # 更新碰撞矩形
        self.rect.center = (self.x, self.y)
        
        # 检查是否超出屏幕边界
        if (self.x < 0 or self.x > SCREEN_WIDTH or 
            self.y < 0 or self.y > SCREEN_HEIGHT):
            self.active = False
    
    def draw(self, screen):
        """绘制子弹"""
        if not self.active:
            return
            
        color = YELLOW if self.owner_type == "player" else RED
        pygame.draw.circle(screen, color, (int(self.x), int(self.y)), self.size)
        
        # 绘制子弹轨迹效果
        trail_color = (color[0]//2, color[1]//2, color[2]//2)
        trail_x = self.x - self.dx * 0.5
        trail_y = self.y - self.dy * 0.5
        pygame.draw.circle(screen, trail_color, (int(trail_x), int(trail_y)), self.size//2)
    
    def get_rect(self):
        """获取碰撞矩形"""
        return self.rect
    
    def destroy(self):
        """销毁子弹"""
        self.active = False
