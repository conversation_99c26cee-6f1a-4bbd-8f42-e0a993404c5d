"""
爆炸效果系统
"""
import pygame
import random
import math
from utils import *

class Explosion:
    def __init__(self, x, y, explosion_type="normal"):
        self.x = x
        self.y = y
        self.type = explosion_type
        self.active = True
        self.timer = 0
        self.max_duration = 30  # 爆炸持续帧数
        
        # 爆炸粒子
        self.particles = []
        self.create_particles()
        
        # 爆炸环效果
        self.rings = []
        self.create_rings()
    
    def create_particles(self):
        """创建爆炸粒子"""
        particle_count = 15 if self.type == "normal" else 25
        
        for _ in range(particle_count):
            # 随机方向和速度
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(2, 8)
            
            particle = {
                'x': self.x,
                'y': self.y,
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed,
                'life': random.randint(15, 30),
                'max_life': random.randint(15, 30),
                'size': random.randint(2, 6),
                'color_type': random.choice(['fire', 'smoke'])
            }
            self.particles.append(particle)
    
    def create_rings(self):
        """创建爆炸环效果"""
        ring_count = 2 if self.type == "normal" else 3
        
        for i in range(ring_count):
            ring = {
                'radius': 5,
                'max_radius': 25 + i * 10,
                'width': 3,
                'alpha': 255,
                'delay': i * 3
            }
            self.rings.append(ring)
    
    def update(self):
        """更新爆炸效果"""
        if not self.active:
            return
        
        self.timer += 1
        
        # 更新粒子
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['vy'] += 0.2  # 重力效果
            particle['vx'] *= 0.98  # 空气阻力
            particle['life'] -= 1
            
            if particle['life'] <= 0:
                self.particles.remove(particle)
        
        # 更新爆炸环
        for ring in self.rings:
            if self.timer > ring['delay']:
                ring['radius'] += 2
                ring['alpha'] = max(0, ring['alpha'] - 8)
        
        # 检查爆炸是否结束
        if self.timer >= self.max_duration and not self.particles:
            self.active = False
    
    def draw(self, screen):
        """绘制爆炸效果"""
        if not self.active:
            return
        
        # 绘制爆炸环
        for ring in self.rings:
            if self.timer > ring['delay'] and ring['alpha'] > 0:
                if ring['radius'] <= ring['max_radius']:
                    # 计算环的颜色强度，确保在0-1范围内
                    intensity = max(0.0, min(1.0, ring['alpha'] / 255.0))

                    # 外环 - 橙色，确保颜色值在0-255范围内
                    outer_r = max(0, min(255, int(255 * intensity)))
                    outer_g = max(0, min(255, int(165 * intensity)))
                    outer_color = (outer_r, outer_g, 0)

                    # 确保坐标和半径有效
                    center_x = max(0, min(SCREEN_WIDTH, int(self.x)))
                    center_y = max(0, min(SCREEN_HEIGHT, int(self.y)))
                    radius = max(1, ring['radius'])
                    width = max(1, ring['width'])

                    try:
                        pygame.draw.circle(screen, outer_color, (center_x, center_y), radius, width)

                        # 内环 - 红色
                        if ring['radius'] > 5:
                            inner_r = max(0, min(255, int(255 * intensity)))
                            inner_g = max(0, min(255, int(69 * intensity)))
                            inner_color = (inner_r, inner_g, 0)
                            inner_radius = max(1, ring['radius'] - 3)
                            inner_width = max(1, ring['width'] - 1)
                            pygame.draw.circle(screen, inner_color, (center_x, center_y), inner_radius, inner_width)
                    except:
                        # 如果绘制失败，跳过这个环
                        continue
        
        # 绘制粒子
        for particle in self.particles:
            if particle['life'] > 0:
                # 计算粒子亮度（基于生命值）
                life_ratio = max(0.0, min(1.0, particle['life'] / particle['max_life']))

                # 根据类型和生命值选择颜色
                if particle['color_type'] == 'fire':
                    if life_ratio > 0.7:
                        color = (255, 255, 255)  # 白色
                    elif life_ratio > 0.4:
                        color = (255, 255, 0)    # 黄色
                    else:
                        color = (255, 165, 0)    # 橙色
                else:  # smoke
                    # 烟雾颜色随时间变暗，确保值在0-255范围内
                    gray_value = max(0, min(255, int(128 * life_ratio)))
                    color = (gray_value, gray_value, gray_value)

                # 粒子大小随时间减小，确保大小至少为1
                current_size = max(1, int(particle['size'] * life_ratio))

                # 确保坐标是整数且在合理范围内
                x = max(0, min(SCREEN_WIDTH, int(particle['x'])))
                y = max(0, min(SCREEN_HEIGHT, int(particle['y'])))

                # 直接绘制粒子
                try:
                    pygame.draw.circle(screen, color, (x, y), current_size)
                except:
                    # 如果绘制失败，跳过这个粒子
                    continue

class ExplosionManager:
    def __init__(self):
        self.explosions = []
    
    def add_explosion(self, x, y, explosion_type="normal"):
        """添加爆炸效果"""
        explosion = Explosion(x, y, explosion_type)
        self.explosions.append(explosion)
    
    def update(self):
        """更新所有爆炸效果"""
        for explosion in self.explosions[:]:
            explosion.update()
            if not explosion.active:
                self.explosions.remove(explosion)
    
    def draw(self, screen):
        """绘制所有爆炸效果"""
        for explosion in self.explosions:
            explosion.draw(screen)
    
    def clear(self):
        """清除所有爆炸效果"""
        self.explosions.clear()
