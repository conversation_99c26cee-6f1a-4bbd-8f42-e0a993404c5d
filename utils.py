"""
游戏工具函数和常量定义
"""
import pygame
import math

# 游戏常量
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 128, 0)
BROWN = (139, 69, 19)

# 坦克常量
TANK_SIZE = 30
TANK_SPEED = 3
BULLET_SPEED = 8
BULLET_SIZE = 5

# 游戏状态
GAME_MENU = 0
GAME_PLAYING = 1
GAME_OVER = 2
GAME_WIN = 3

def distance(pos1, pos2):
    """计算两点间距离"""
    return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

def normalize_angle(angle):
    """标准化角度到0-360度"""
    while angle < 0:
        angle += 360
    while angle >= 360:
        angle -= 360
    return angle

def angle_to_vector(angle):
    """将角度转换为单位向量"""
    rad = math.radians(angle)
    return (math.cos(rad), math.sin(rad))

def vector_to_angle(dx, dy):
    """将向量转换为角度"""
    return math.degrees(math.atan2(dy, dx))

def check_collision_rect(rect1, rect2):
    """检查两个矩形是否碰撞"""
    return rect1.colliderect(rect2)

def clamp(value, min_val, max_val):
    """限制值在指定范围内"""
    return max(min_val, min(value, max_val))
