"""
坦克类定义
"""
import pygame
import math
import random
import time
from utils import *
from bullet import Bullet

class Tank:
    def __init__(self, x, y, tank_type="player"):
        self.x = x
        self.y = y
        self.angle = 0  # 坦克朝向角度
        self.speed = TANK_SPEED
        self.size = TANK_SIZE
        self.tank_type = tank_type
        self.health = 3 if tank_type == "player" else 1
        self.max_health = self.health
        
        # 射击相关
        self.last_shot_time = 0
        self.shot_cooldown = 500  # 毫秒
        
        # 创建碰撞矩形
        self.rect = pygame.Rect(x - self.size//2, y - self.size//2, self.size, self.size)
        
        # 道具效果
        self.powerups = {}  # 存储当前激活的道具效果
        self.powerup_timers = {}  # 存储道具效果的计时器

        # AI相关（仅敌方坦克）
        if tank_type == "enemy":
            self.ai_timer = 0
            self.ai_direction_change_time = random.randint(1000, 3000)
            self.target_angle = random.randint(0, 360)
            self.ai_shoot_timer = random.randint(500, 1500)  # 更频繁的射击
    
    def update(self, keys_pressed=None, obstacles=None, player_tank=None, other_tanks=None):
        """更新坦克状态"""
        # 更新道具效果
        self.update_powerups()

        if self.tank_type == "player" and keys_pressed:
            self.handle_player_input(keys_pressed, obstacles, other_tanks)
        elif self.tank_type == "enemy":
            return self.handle_ai_behavior(obstacles, player_tank, other_tanks)

        # 更新碰撞矩形
        self.rect.center = (self.x, self.y)
        return None
    
    def handle_player_input(self, keys_pressed, obstacles, other_tanks=None):
        """处理玩家输入"""
        old_x, old_y = self.x, self.y

        # 移动控制
        if keys_pressed[pygame.K_w]:
            self.angle = 270  # 向上
            self.move_forward(obstacles, other_tanks)
        elif keys_pressed[pygame.K_s]:
            self.angle = 90   # 向下
            self.move_forward(obstacles, other_tanks)
        elif keys_pressed[pygame.K_a]:
            self.angle = 180  # 向左
            self.move_forward(obstacles, other_tanks)
        elif keys_pressed[pygame.K_d]:
            self.angle = 0    # 向右
            self.move_forward(obstacles, other_tanks)
    
    def handle_ai_behavior(self, obstacles, player_tank=None, other_tanks=None):
        """处理AI行为"""
        current_time = pygame.time.get_ticks()

        # 改变方向
        self.ai_timer += 16  # 假设60FPS，每帧约16ms
        if self.ai_timer >= self.ai_direction_change_time:
            self.target_angle = random.choice([0, 90, 180, 270])
            self.ai_timer = 0
            self.ai_direction_change_time = random.randint(1000, 3000)

        # 移动
        self.angle = self.target_angle
        if random.random() < 0.8:  # 80%的时间在移动
            self.move_forward(obstacles, other_tanks)

        # AI射击 - 更频繁且更智能
        self.ai_shoot_timer -= 16
        if self.ai_shoot_timer <= 0:
            # 重置射击计时器，更短的间隔
            self.ai_shoot_timer = random.randint(800, 2000)

            # 如果有玩家坦克，有30%的概率朝玩家方向射击
            if player_tank and random.random() < 0.3:
                # 计算朝向玩家的角度
                dx = player_tank.x - self.x
                dy = player_tank.y - self.y
                if abs(dx) > abs(dy):
                    # 水平方向
                    self.angle = 0 if dx > 0 else 180
                else:
                    # 垂直方向
                    self.angle = 90 if dy > 0 else 270

            return self.shoot()

        return None
    
    def move_forward(self, obstacles, other_tanks=None):
        """向前移动"""
        old_x, old_y = self.x, self.y

        # 计算新位置
        rad = math.radians(self.angle)
        new_x = self.x + math.cos(rad) * self.speed
        new_y = self.y + math.sin(rad) * self.speed

        # 检查边界
        half_size = self.size // 2
        if (new_x - half_size < 20 or new_x + half_size > SCREEN_WIDTH - 20 or
            new_y - half_size < 20 or new_y + half_size > SCREEN_HEIGHT - 20):
            return

        # 临时更新位置检查碰撞
        self.x, self.y = new_x, new_y
        self.rect.center = (self.x, self.y)

        # 检查与障碍物的碰撞
        if obstacles and obstacles.check_collision(self.rect):
            # 恢复原位置
            self.x, self.y = old_x, old_y
            self.rect.center = (self.x, self.y)
            return

        # 检查与其他坦克的碰撞
        if other_tanks:
            for other_tank in other_tanks:
                if other_tank != self and self.rect.colliderect(other_tank.get_rect()):
                    # 恢复原位置
                    self.x, self.y = old_x, old_y
                    self.rect.center = (self.x, self.y)
                    return
    
    def shoot(self):
        """射击"""
        current_time = pygame.time.get_ticks()
        if current_time - self.last_shot_time >= self.shot_cooldown:
            self.last_shot_time = current_time

            # 计算子弹起始位置（坦克前方）
            rad = math.radians(self.angle)
            bullet_x = self.x + math.cos(rad) * (self.size // 2 + 5)
            bullet_y = self.y + math.sin(rad) * (self.size // 2 + 5)

            # 检查是否有破墙道具
            can_break_walls = self.has_powerup("wall_breaker")

            return Bullet(bullet_x, bullet_y, self.angle, self.tank_type, can_break_walls)
        return None
    
    def take_damage(self):
        """受到伤害"""
        # 检查是否有护盾效果
        if "shield" in self.powerups:
            return False  # 护盾保护，不受伤害

        self.health -= 1
        return self.health <= 0

    def add_powerup(self, powerup_type):
        """添加道具效果"""
        self.powerups[powerup_type] = True
        self.powerup_timers[powerup_type] = time.time()

    def update_powerups(self):
        """更新道具效果"""
        current_time = time.time()
        expired_powerups = []

        for powerup_type, start_time in self.powerup_timers.items():
            if current_time - start_time > 30:  # 30秒后失效
                expired_powerups.append(powerup_type)

        # 移除过期的道具效果
        for powerup_type in expired_powerups:
            if powerup_type in self.powerups:
                del self.powerups[powerup_type]
            if powerup_type in self.powerup_timers:
                del self.powerup_timers[powerup_type]

    def has_powerup(self, powerup_type):
        """检查是否有特定道具效果"""
        return powerup_type in self.powerups
    
    def draw(self, screen):
        """绘制坦克"""
        # 坦克主体颜色
        if self.tank_type == "player":
            body_color = GREEN
            turret_color = DARK_GREEN
        else:
            body_color = RED
            turret_color = (128, 0, 0)
        
        # 绘制坦克主体
        tank_rect = pygame.Rect(int(self.x - self.size//2), int(self.y - self.size//2),
                               self.size, self.size)
        pygame.draw.rect(screen, body_color, tank_rect)
        pygame.draw.rect(screen, BLACK, tank_rect, 2)

        # 绘制炮管
        rad = math.radians(self.angle)
        barrel_length = self.size // 2 + 8
        barrel_end_x = self.x + math.cos(rad) * barrel_length
        barrel_end_y = self.y + math.sin(rad) * barrel_length

        pygame.draw.line(screen, turret_color, (int(self.x), int(self.y)),
                        (int(barrel_end_x), int(barrel_end_y)), 4)
        
        # 绘制炮塔
        pygame.draw.circle(screen, turret_color, (int(self.x), int(self.y)), 8)
        pygame.draw.circle(screen, BLACK, (int(self.x), int(self.y)), 8, 2)
        
        # 绘制道具效果
        self.draw_powerup_effects(screen)

        # 绘制生命值（仅玩家坦克）
        if self.tank_type == "player":
            self.draw_health_bar(screen)
    
    def draw_health_bar(self, screen):
        """绘制生命值条"""
        bar_width = 40
        bar_height = 6
        bar_x = int(self.x - bar_width // 2)
        bar_y = int(self.y - self.size // 2 - 15)

        # 背景
        pygame.draw.rect(screen, RED, (bar_x, bar_y, bar_width, bar_height))

        # 当前生命值
        current_width = int((self.health / self.max_health) * bar_width)
        pygame.draw.rect(screen, GREEN, (bar_x, bar_y, current_width, bar_height))

        # 边框
        pygame.draw.rect(screen, BLACK, (bar_x, bar_y, bar_width, bar_height), 1)

    def draw_powerup_effects(self, screen):
        """绘制道具效果"""
        if not self.powerups:
            return

        # 绘制护盾效果
        if "shield" in self.powerups:
            # 绘制蓝色护盾光环
            shield_radius = self.size // 2 + 8
            for i in range(3):
                alpha = 100 - i * 30
                shield_surface = pygame.Surface((shield_radius * 2 + 4, shield_radius * 2 + 4), pygame.SRCALPHA)
                pygame.draw.circle(shield_surface, (0, 191, 255, alpha),
                                 (shield_radius + 2, shield_radius + 2), shield_radius - i)
                screen.blit(shield_surface, (self.x - shield_radius - 2, self.y - shield_radius - 2))

        # 绘制破墙效果
        if "wall_breaker" in self.powerups:
            # 绘制橙色光环
            breaker_radius = self.size // 2 + 6
            for i in range(2):
                alpha = 80 - i * 40
                breaker_surface = pygame.Surface((breaker_radius * 2 + 4, breaker_radius * 2 + 4), pygame.SRCALPHA)
                pygame.draw.circle(breaker_surface, (255, 165, 0, alpha),
                                 (breaker_radius + 2, breaker_radius + 2), breaker_radius - i)
                screen.blit(breaker_surface, (self.x - breaker_radius - 2, self.y - breaker_radius - 2))
    
    def get_rect(self):
        """获取碰撞矩形"""
        return self.rect
    
    def get_position(self):
        """获取位置"""
        return (self.x, self.y)
