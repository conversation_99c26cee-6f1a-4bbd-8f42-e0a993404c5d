# 坦克大战游戏

一个基于Python和Pygame开发的经典坦克大战游戏。

## 游戏特性

- 🎮 经典坦克大战玩法
- 🤖 智能AI敌方坦克
- 🧱 多种类型障碍物（砖块、钢铁墙）
- 💥 真实的碰撞检测系统
- 🎯 精确的射击系统
- 📊 分数和关卡系统
- 🎨 简洁清晰的像素风格图形

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装pygame：

```bash
pip install pygame==2.5.2
```

### 2. 运行游戏

```bash
python main.py
```

## 游戏控制

### 玩家控制
- **W** - 向上移动
- **A** - 向左移动  
- **S** - 向下移动
- **D** - 向右移动
- **空格** - 射击
- **ESC** - 返回菜单/暂停游戏

### 菜单控制
- **空格** - 开始游戏
- **R** - 重新开始（游戏结束时）
- **ESC** - 返回主菜单

## 游戏元素

### 坦克类型
- **绿色坦克** - 玩家坦克（3点生命值）
- **红色坦克** - 敌方AI坦克（1点生命值）

### 障碍物类型
- **灰色墙体** - 钢铁墙，无法被摧毁
- **棕色砖块** - 可被子弹摧毁
- **边界墙** - 地图边界，无法穿越

### 子弹系统
- **黄色子弹** - 玩家子弹
- **红色子弹** - 敌方子弹
- 子弹具有轨迹效果和碰撞检测

## 游戏规则

1. **目标**: 消灭所有敌方坦克
2. **生命值**: 玩家有3点生命值，被击中会减少
3. **关卡**: 共3个关卡，每关敌人数量递增
4. **分数**: 击毁敌方坦克获得100分
5. **胜利条件**: 通过所有关卡
6. **失败条件**: 玩家生命值归零

## 项目结构

```
tank_battle/
├── main.py          # 游戏入口文件
├── game.py          # 游戏主逻辑和状态管理
├── tank.py          # 坦克类（玩家和AI）
├── bullet.py        # 子弹类
├── obstacle.py      # 障碍物类和地图管理
├── utils.py         # 工具函数和常量
├── requirements.txt # 依赖包列表
└── README.md        # 项目说明文档
```

## 技术特性

- **面向对象设计** - 清晰的类结构和职责分离
- **碰撞检测** - 精确的矩形碰撞检测算法
- **AI系统** - 敌方坦克的智能行为模式
- **状态管理** - 完整的游戏状态机
- **事件驱动** - 响应式的用户输入处理
- **模块化架构** - 易于扩展和维护

## 开发说明

### 添加新功能
- 在对应的类文件中添加新方法
- 在`utils.py`中添加新的常量
- 在`game.py`中集成新功能

### 修改游戏平衡
- 调整`utils.py`中的游戏常量
- 修改坦克生命值和移动速度
- 调整AI行为参数

### 自定义地图
- 在`obstacle.py`的`ObstacleManager.create_default_map()`中修改地图布局
- 添加新的障碍物类型

## 故障排除

### 常见问题
1. **pygame未安装**: 运行`pip install pygame`
2. **Python版本**: 确保使用Python 3.6+
3. **音频问题**: 游戏目前不包含音效，如需添加请安装音频驱动

### 性能优化
- 游戏运行在60FPS
- 如果性能不佳，可以在`utils.py`中调整FPS常量

## 许可证

本项目仅供学习和娱乐使用。

## 贡献

欢迎提交问题报告和功能建议！
