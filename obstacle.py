"""
障碍物类定义
"""
import pygame
from utils import *

class Obstacle:
    def __init__(self, x, y, width, height, obstacle_type="wall"):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.type = obstacle_type
        self.rect = pygame.Rect(x, y, width, height)
        
        # 根据类型设置属性
        if obstacle_type == "wall":
            self.color = GRAY
            self.destructible = False
        elif obstacle_type == "brick":
            self.color = BROWN
            self.destructible = True
            self.health = 1
        elif obstacle_type == "steel":
            self.color = (169, 169, 169)  # 浅灰色
            self.destructible = False
    
    def draw(self, screen):
        """绘制障碍物"""
        pygame.draw.rect(screen, self.color, self.rect)
        
        # 绘制边框
        border_color = (self.color[0]//2, self.color[1]//2, self.color[2]//2)
        pygame.draw.rect(screen, border_color, self.rect, 2)
        
        # 为砖块添加纹理效果
        if self.type == "brick":
            # 绘制砖块纹理
            for i in range(0, self.width, 10):
                for j in range(0, self.height, 5):
                    if (i + j) % 20 == 0:
                        pygame.draw.rect(screen, (self.color[0]-20, self.color[1]-20, self.color[2]-20), 
                                       (self.x + i, self.y + j, 8, 3))
    
    def get_rect(self):
        """获取碰撞矩形"""
        return self.rect
    
    def take_damage(self):
        """受到伤害"""
        if self.destructible:
            self.health -= 1
            return self.health <= 0
        return False

class ObstacleManager:
    def __init__(self):
        self.obstacles = []
        self.create_default_map()
    
    def create_default_map(self):
        """创建默认地图"""
        # 边界墙
        self.obstacles.append(Obstacle(0, 0, SCREEN_WIDTH, 20, "steel"))  # 上边界
        self.obstacles.append(Obstacle(0, SCREEN_HEIGHT-20, SCREEN_WIDTH, 20, "steel"))  # 下边界
        self.obstacles.append(Obstacle(0, 0, 20, SCREEN_HEIGHT, "steel"))  # 左边界
        self.obstacles.append(Obstacle(SCREEN_WIDTH-20, 0, 20, SCREEN_HEIGHT, "steel"))  # 右边界
        
        # 内部障碍物
        # 砖块墙
        for i in range(100, 300, 40):
            self.obstacles.append(Obstacle(i, 150, 30, 30, "brick"))
            self.obstacles.append(Obstacle(i, 350, 30, 30, "brick"))
        
        for i in range(500, 700, 40):
            self.obstacles.append(Obstacle(i, 200, 30, 30, "brick"))
            self.obstacles.append(Obstacle(i, 400, 30, 30, "brick"))
        
        # 钢铁墙
        self.obstacles.append(Obstacle(350, 250, 100, 30, "steel"))
        self.obstacles.append(Obstacle(350, 320, 100, 30, "steel"))
        
        # 中央掩体
        self.obstacles.append(Obstacle(375, 275, 50, 50, "brick"))
    
    def add_obstacle(self, obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
    
    def remove_obstacle(self, obstacle):
        """移除障碍物"""
        if obstacle in self.obstacles:
            self.obstacles.remove(obstacle)
    
    def check_collision(self, rect):
        """检查与障碍物的碰撞"""
        for obstacle in self.obstacles:
            if obstacle.get_rect().colliderect(rect):
                return obstacle
        return None
    
    def handle_bullet_collision(self, bullet):
        """处理子弹与障碍物的碰撞"""
        for obstacle in self.obstacles[:]:  # 使用切片复制列表，避免迭代时修改
            if obstacle.get_rect().colliderect(bullet.get_rect()):
                bullet.destroy()
                if obstacle.take_damage():
                    self.remove_obstacle(obstacle)
                return True
        return False
    
    def draw(self, screen):
        """绘制所有障碍物"""
        for obstacle in self.obstacles:
            obstacle.draw(screen)
    
    def get_obstacles(self):
        """获取所有障碍物"""
        return self.obstacles
